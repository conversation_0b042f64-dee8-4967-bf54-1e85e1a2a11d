import React, { useState, useEffect } from 'react';
import { useAppData } from '../../context/AppDataContext';
import {
  Card, Form, Button, Image, Spinner, Alert, Row, Col
} from 'react-bootstrap';
import {
  Save, Settings, Globe, Calendar, Image as ImageIcon,
  Upload, Info, Shield
} from 'lucide-react';
import NotificationModal from '../common/NotificationModal';

const GeneralSettings = () => {
  const { appData, updateSiteConfig, loadSiteConfig } = useAppData();

  const [websiteName, setWebsiteName] = useState('');
  const [registrationOpenDate, setRegistrationOpenDate] = useState('');
  const [registrationCloseDate, setRegistrationCloseDate] = useState('');
  const [logoPreview, setLogoPreview] = useState('');
  const [selectedLogoFile, setSelectedLogoFile] = useState(null);

  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const BASE_URL = 'http://localhost:8083';

useEffect(() => {
  if (appData) {
    setWebsiteName(appData.websiteName || '');
    setRegistrationOpenDate(appData.registrationOpenDate || '');
    setRegistrationCloseDate(appData.registrationCloseDate || '');

    if (appData.logoPath) {
      const normalizedPath = appData.logoPath.replace(/\\/g, '/');
      const fullUrl = `${BASE_URL}/${normalizedPath}`;
      setLogoPreview(fullUrl);
    } else {
      setLogoPreview('/logo512.png');
    }
  }
}, [appData]);


  const handleLogoChange = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      setError('Image file size must be less than 5MB');
      return;
    }

    setSelectedLogoFile(file);
    setLogoPreview(URL.createObjectURL(file));
    setError('');
  };

  const handleSave = async () => {
    if (!websiteName.trim()) {
      setError('Site name is required');
      return;
    }

    setIsSaving(true);
    setError('');

    try {
      await updateSiteConfig({
        websiteName: websiteName.trim(),
        registrationOpenDate: registrationOpenDate.trim(),
        registrationCloseDate: registrationCloseDate.trim(),
      }, selectedLogoFile);

      await loadSiteConfig();
      setShowSuccessModal(true);
    } catch (err) {
      setError('Error saving settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      <Card className="border-0 shadow-sm">
      <Card.Header className="d-flex align-items-center">
        <Settings size={20} className="me-2" />
        Site & Registration Configuration
      </Card.Header>
      <Card.Body>
        {error && <Alert variant="danger" className="mb-4">{error}</Alert>}

        <Form>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-4">
                <Form.Label className="d-flex align-items-center">
                  <Globe size={16} className="me-2 text-primary" />
                  Site Name *
                </Form.Label>
                <Form.Control
                  type="text"
                  value={websiteName}
                  onChange={(e) => setWebsiteName(e.target.value)}
                  placeholder="Enter your site name"
                  required
                  className="form-control-lg"
                />
                <Form.Text className="text-muted">
                  This will appear in the navigation and page titles
                </Form.Text>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-4">
                <Form.Label className="d-flex align-items-center">
                  <Calendar size={16} className="me-2 text-success" />
                  Registration Start Date
                </Form.Label>
                <Form.Control
                  type="date"
                  value={registrationOpenDate}
                  onChange={(e) => setRegistrationOpenDate(e.target.value)}
                  className="form-control-lg"
                />
                <Form.Text className="text-muted">
                  When registration opens
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-4">
                <Form.Label className="d-flex align-items-center">
                  <Calendar size={16} className="me-2 text-danger" />
                  Registration End Date
                </Form.Label>
                <Form.Control
                  type="date"
                  value={registrationCloseDate}
                  onChange={(e) => setRegistrationCloseDate(e.target.value)}
                  className="form-control-lg"
                />
                <Form.Text className="text-muted">
                  When registration closes
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-4">
            <Form.Label className="d-flex align-items-center">
              <ImageIcon size={16} className="me-2 text-primary" />
              Site Logo
            </Form.Label>
            <div className="p-4 border border-2 border-dashed rounded-3 bg-light">
              <Row className="align-items-center">
                <Col md={3} className="text-center">
                  <div className="position-relative d-inline-block">
   <Image
  src={logoPreview}
  roundedCircle
  width="80"
  height="80"
  className="border border-3 border-white shadow-sm"
  onError={(e) => {
    e.target.onerror = null;
    e.target.src = '/logo512.png'; // fallback default logo
  }}
/>


                    <div className="position-absolute bottom-0 end-0 bg-primary rounded-circle p-1">
                      <Upload size={12} className="text-white" />
                    </div>
                  </div>
                </Col>
                <Col md={9}>
                  <Form.Control
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="mb-2"
                  />
                  <div className="d-flex align-items-center text-muted small">
                    <Info size={14} className="me-2" />
                    Upload an image file (max 5MB). Supported: JPG, PNG, GIF, SVG
                  </div>
                </Col>
              </Row>
            </div>
          </Form.Group>
        </Form>
      </Card.Body>

      <Card.Footer className="d-flex justify-content-between align-items-center">
        <small className="text-muted">
          <Shield size={14} className="me-1" />
          Changes are saved to the backend
        </small>
        <Button
          variant="primary"
          onClick={handleSave}
          disabled={isSaving}
          className="px-4"
        >
          {isSaving ? (
            <>
              <Spinner as="span" animation="border" size="sm" className="me-2" />
              Saving...
            </>
          ) : (
            <>
              <Save size={18} className="me-2" />
              Save Settings
            </>
          )}
        </Button>
      </Card.Footer>
      </Card>

      <NotificationModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Settings Saved"
        message="Your site settings have been saved successfully!"
        type="success"
      />
    </>
  );
};

export default GeneralSettings;
