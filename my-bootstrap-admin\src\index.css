/* Modern Admin Dashboard Styles */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --info-color: #0891b2;
  --light-bg: #f8fafc;
  --dark-bg: #0f172a;
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --border-radius: 0.75rem;
  --transition: all 0.2s ease-in-out;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--light-bg);
  color: #1e293b;
  line-height: 1.6;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom Card Styles */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  background: white;
}

.card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-1px);
}

.card-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  font-size: 1.1rem;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background: #f8fafc;
  border: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  padding: 1rem 1.5rem;
}

/* Modern Button Styles */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: var(--transition);
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}

.btn-outline-danger {
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  background: transparent;
}

.btn-outline-danger:hover {
  background: var(--danger-color);
  border-color: var(--danger-color);
  transform: translateY(-1px);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  transition: var(--transition);
  font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* List Group */
.list-group-item {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem !important;
  margin-bottom: 0.5rem;
  transition: var(--transition);
  padding: 1rem 1.25rem;
}

.list-group-item:hover {
  background-color: #f9fafb;
  transform: translateX(2px);
}

.list-group-item:last-child {
  margin-bottom: 0;
}

/* Alert Styles */
.alert {
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 1.25rem;
  font-weight: 500;
}

.alert-danger {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: var(--danger-color);
  border-left: 4px solid var(--danger-color);
}

/* Badge Styles */
.badge {
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
}

/* Navbar Styles */
.navbar-dark {
  background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Accordion Styles */
.accordion-item {
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem !important;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.accordion-header button {
  background: #f8fafc;
  border: none;
  font-weight: 600;
  color: #374151;
  padding: 1.25rem 1.5rem;
}

.accordion-header button:not(.collapsed) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.accordion-body {
  padding: 1.5rem;
}

/* Custom Utilities */
.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 0 0 1.5rem 1.5rem;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .admin-header {
    padding: 1.5rem 0;
    margin-bottom: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
