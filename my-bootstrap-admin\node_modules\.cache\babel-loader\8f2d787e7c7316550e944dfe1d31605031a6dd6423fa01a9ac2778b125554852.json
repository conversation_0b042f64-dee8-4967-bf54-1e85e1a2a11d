{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\GitHub\\\\SITE2025\\\\my-bootstrap-admin\\\\src\\\\components\\\\admin\\\\GeneralSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAppData } from '../../context/AppDataContext';\nimport { Card, Form, Button, Image, Spinner, Alert, Row, Col } from 'react-bootstrap';\nimport { Save, Settings, Globe, Calendar, Image as ImageIcon, Upload, Info, Shield } from 'lucide-react';\nimport NotificationModal from '../common/NotificationModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GeneralSettings = () => {\n  _s();\n  const {\n    appData,\n    updateSiteConfig,\n    loadSiteConfig\n  } = useAppData();\n  const [websiteName, setWebsiteName] = useState('');\n  const [registrationOpenDate, setRegistrationOpenDate] = useState('');\n  const [registrationCloseDate, setRegistrationCloseDate] = useState('');\n  const [logoPreview, setLogoPreview] = useState('');\n  const [selectedLogoFile, setSelectedLogoFile] = useState(null);\n  const [isSaving, setIsSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const BASE_URL = 'http://localhost:8083';\n  useEffect(() => {\n    if (appData) {\n      setWebsiteName(appData.websiteName || '');\n      setRegistrationOpenDate(appData.registrationOpenDate || '');\n      setRegistrationCloseDate(appData.registrationCloseDate || '');\n      if (appData.logoPath) {\n        const normalizedPath = appData.logoPath.replace(/\\\\/g, '/');\n        const fullUrl = `${BASE_URL}/${normalizedPath}`;\n        setLogoPreview(fullUrl);\n      } else {\n        setLogoPreview('/logo512.png');\n      }\n    }\n  }, [appData]);\n  const handleLogoChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (!file) return;\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file');\n      return;\n    }\n    if (file.size > 5 * 1024 * 1024) {\n      setError('Image file size must be less than 5MB');\n      return;\n    }\n    setSelectedLogoFile(file);\n    setLogoPreview(URL.createObjectURL(file));\n    setError('');\n  };\n  const handleSave = async () => {\n    if (!websiteName.trim()) {\n      setError('Site name is required');\n      return;\n    }\n    setIsSaving(true);\n    setError('');\n    try {\n      await updateSiteConfig({\n        websiteName: websiteName.trim(),\n        registrationOpenDate: registrationOpenDate.trim(),\n        registrationCloseDate: registrationCloseDate.trim()\n      }, selectedLogoFile);\n      await loadSiteConfig();\n      alert('Settings saved successfully!');\n    } catch (err) {\n      setError('Error saving settings. Please try again.');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"border-0 shadow-sm\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"d-flex align-items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Settings, {\n        size: 20,\n        className: \"me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), \"Site & Registration Configuration\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Globe, {\n                  size: 16,\n                  className: \"me-2 text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), \"Site Name *\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"text\",\n                value: websiteName,\n                onChange: e => setWebsiteName(e.target.value),\n                placeholder: \"Enter your site name\",\n                required: true,\n                className: \"form-control-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"This will appear in the navigation and page titles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 16,\n                  className: \"me-2 text-success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), \"Registration Start Date\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"date\",\n                value: registrationOpenDate,\n                onChange: e => setRegistrationOpenDate(e.target.value),\n                className: \"form-control-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"When registration opens\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 16,\n                  className: \"me-2 text-danger\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), \"Registration End Date\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"date\",\n                value: registrationCloseDate,\n                onChange: e => setRegistrationCloseDate(e.target.value),\n                className: \"form-control-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"When registration closes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n              size: 16,\n              className: \"me-2 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), \"Site Logo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border border-2 border-dashed rounded-3 bg-light\",\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              className: \"align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 3,\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-relative d-inline-block\",\n                  children: [/*#__PURE__*/_jsxDEV(Image, {\n                    src: logoPreview,\n                    roundedCircle: true,\n                    width: \"80\",\n                    height: \"80\",\n                    className: \"border border-3 border-white shadow-sm\",\n                    onError: e => {\n                      e.target.onerror = null;\n                      e.target.src = '/logo512.png'; // fallback default logo\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 4\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"position-absolute bottom-0 end-0 bg-primary rounded-circle p-1\",\n                    children: /*#__PURE__*/_jsxDEV(Upload, {\n                      size: 12,\n                      className: \"text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 9,\n                children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  accept: \"image/*\",\n                  onChange: handleLogoChange,\n                  className: \"mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center text-muted small\",\n                  children: [/*#__PURE__*/_jsxDEV(Info, {\n                    size: 14,\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), \"Upload an image file (max 5MB). Supported: JPG, PNG, GIF, SVG\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Footer, {\n      className: \"d-flex justify-content-between align-items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-muted\",\n        children: [/*#__PURE__*/_jsxDEV(Shield, {\n          size: 14,\n          className: \"me-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), \"Changes are saved to the backend\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: handleSave,\n        disabled: isSaving,\n        className: \"px-4\",\n        children: isSaving ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n            as: \"span\",\n            animation: \"border\",\n            size: \"sm\",\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), \"Saving...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 18,\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), \"Save Settings\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(GeneralSettings, \"R2VA1420q8rWO0B+9CWO/EjDTfw=\", false, function () {\n  return [useAppData];\n});\n_c = GeneralSettings;\nexport default GeneralSettings;\nvar _c;\n$RefreshReg$(_c, \"GeneralSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAppData", "Card", "Form", "<PERSON><PERSON>", "Image", "Spinner", "<PERSON><PERSON>", "Row", "Col", "Save", "Settings", "Globe", "Calendar", "ImageIcon", "Upload", "Info", "Shield", "NotificationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GeneralSettings", "_s", "appData", "updateSiteConfig", "loadSiteConfig", "websiteName", "setWebsiteName", "registrationOpenDate", "setRegistrationOpenDate", "registrationCloseDate", "setRegistrationCloseDate", "logoPreview", "setLogoPreview", "selectedLogoFile", "setSelectedLogoFile", "isSaving", "setIsSaving", "error", "setError", "showSuccessModal", "setShowSuccessModal", "BASE_URL", "logoPath", "normalizedPath", "replace", "fullUrl", "handleLogoChange", "e", "_e$target$files", "file", "target", "files", "type", "startsWith", "size", "URL", "createObjectURL", "handleSave", "trim", "alert", "err", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "md", "Group", "Label", "Control", "value", "onChange", "placeholder", "required", "Text", "src", "roundedCircle", "width", "height", "onError", "onerror", "accept", "Footer", "onClick", "disabled", "as", "animation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/SITE2025/my-bootstrap-admin/src/components/admin/GeneralSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useAppData } from '../../context/AppDataContext';\r\nimport {\r\n  Card, Form, Button, Image, Spinner, Alert, Row, Col\r\n} from 'react-bootstrap';\r\nimport {\r\n  Save, Settings, Globe, Calendar, Image as ImageIcon,\r\n  Upload, Info, Shield\r\n} from 'lucide-react';\r\nimport NotificationModal from '../common/NotificationModal';\r\n\r\nconst GeneralSettings = () => {\r\n  const { appData, updateSiteConfig, loadSiteConfig } = useAppData();\r\n\r\n  const [websiteName, setWebsiteName] = useState('');\r\n  const [registrationOpenDate, setRegistrationOpenDate] = useState('');\r\n  const [registrationCloseDate, setRegistrationCloseDate] = useState('');\r\n  const [logoPreview, setLogoPreview] = useState('');\r\n  const [selectedLogoFile, setSelectedLogoFile] = useState(null);\r\n\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const BASE_URL = 'http://localhost:8083';\r\n\r\nuseEffect(() => {\r\n  if (appData) {\r\n    setWebsiteName(appData.websiteName || '');\r\n    setRegistrationOpenDate(appData.registrationOpenDate || '');\r\n    setRegistrationCloseDate(appData.registrationCloseDate || '');\r\n\r\n    if (appData.logoPath) {\r\n      const normalizedPath = appData.logoPath.replace(/\\\\/g, '/');\r\n      const fullUrl = `${BASE_URL}/${normalizedPath}`;\r\n      setLogoPreview(fullUrl);\r\n    } else {\r\n      setLogoPreview('/logo512.png');\r\n    }\r\n  }\r\n}, [appData]);\r\n\r\n\r\n  const handleLogoChange = (e) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    if (!file.type.startsWith('image/')) {\r\n      setError('Please select a valid image file');\r\n      return;\r\n    }\r\n\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      setError('Image file size must be less than 5MB');\r\n      return;\r\n    }\r\n\r\n    setSelectedLogoFile(file);\r\n    setLogoPreview(URL.createObjectURL(file));\r\n    setError('');\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!websiteName.trim()) {\r\n      setError('Site name is required');\r\n      return;\r\n    }\r\n\r\n    setIsSaving(true);\r\n    setError('');\r\n\r\n    try {\r\n      await updateSiteConfig({\r\n        websiteName: websiteName.trim(),\r\n        registrationOpenDate: registrationOpenDate.trim(),\r\n        registrationCloseDate: registrationCloseDate.trim(),\r\n      }, selectedLogoFile);\r\n\r\n      await loadSiteConfig();\r\n      alert('Settings saved successfully!');\r\n    } catch (err) {\r\n      setError('Error saving settings. Please try again.');\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"border-0 shadow-sm\">\r\n      <Card.Header className=\"d-flex align-items-center\">\r\n        <Settings size={20} className=\"me-2\" />\r\n        Site & Registration Configuration\r\n      </Card.Header>\r\n      <Card.Body>\r\n        {error && <Alert variant=\"danger\" className=\"mb-4\">{error}</Alert>}\r\n\r\n        <Form>\r\n          <Row>\r\n            <Col md={6}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label className=\"d-flex align-items-center\">\r\n                  <Globe size={16} className=\"me-2 text-primary\" />\r\n                  Site Name *\r\n                </Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  value={websiteName}\r\n                  onChange={(e) => setWebsiteName(e.target.value)}\r\n                  placeholder=\"Enter your site name\"\r\n                  required\r\n                  className=\"form-control-lg\"\r\n                />\r\n                <Form.Text className=\"text-muted\">\r\n                  This will appear in the navigation and page titles\r\n                </Form.Text>\r\n              </Form.Group>\r\n            </Col>\r\n\r\n            <Col md={6}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label className=\"d-flex align-items-center\">\r\n                  <Calendar size={16} className=\"me-2 text-success\" />\r\n                  Registration Start Date\r\n                </Form.Label>\r\n                <Form.Control\r\n                  type=\"date\"\r\n                  value={registrationOpenDate}\r\n                  onChange={(e) => setRegistrationOpenDate(e.target.value)}\r\n                  className=\"form-control-lg\"\r\n                />\r\n                <Form.Text className=\"text-muted\">\r\n                  When registration opens\r\n                </Form.Text>\r\n              </Form.Group>\r\n            </Col>\r\n          </Row>\r\n\r\n          <Row>\r\n            <Col md={6}>\r\n              <Form.Group className=\"mb-4\">\r\n                <Form.Label className=\"d-flex align-items-center\">\r\n                  <Calendar size={16} className=\"me-2 text-danger\" />\r\n                  Registration End Date\r\n                </Form.Label>\r\n                <Form.Control\r\n                  type=\"date\"\r\n                  value={registrationCloseDate}\r\n                  onChange={(e) => setRegistrationCloseDate(e.target.value)}\r\n                  className=\"form-control-lg\"\r\n                />\r\n                <Form.Text className=\"text-muted\">\r\n                  When registration closes\r\n                </Form.Text>\r\n              </Form.Group>\r\n            </Col>\r\n          </Row>\r\n\r\n          <Form.Group className=\"mb-4\">\r\n            <Form.Label className=\"d-flex align-items-center\">\r\n              <ImageIcon size={16} className=\"me-2 text-primary\" />\r\n              Site Logo\r\n            </Form.Label>\r\n            <div className=\"p-4 border border-2 border-dashed rounded-3 bg-light\">\r\n              <Row className=\"align-items-center\">\r\n                <Col md={3} className=\"text-center\">\r\n                  <div className=\"position-relative d-inline-block\">\r\n   <Image\r\n  src={logoPreview}\r\n  roundedCircle\r\n  width=\"80\"\r\n  height=\"80\"\r\n  className=\"border border-3 border-white shadow-sm\"\r\n  onError={(e) => {\r\n    e.target.onerror = null;\r\n    e.target.src = '/logo512.png'; // fallback default logo\r\n  }}\r\n/>\r\n\r\n\r\n                    <div className=\"position-absolute bottom-0 end-0 bg-primary rounded-circle p-1\">\r\n                      <Upload size={12} className=\"text-white\" />\r\n                    </div>\r\n                  </div>\r\n                </Col>\r\n                <Col md={9}>\r\n                  <Form.Control\r\n                    type=\"file\"\r\n                    accept=\"image/*\"\r\n                    onChange={handleLogoChange}\r\n                    className=\"mb-2\"\r\n                  />\r\n                  <div className=\"d-flex align-items-center text-muted small\">\r\n                    <Info size={14} className=\"me-2\" />\r\n                    Upload an image file (max 5MB). Supported: JPG, PNG, GIF, SVG\r\n                  </div>\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          </Form.Group>\r\n        </Form>\r\n      </Card.Body>\r\n\r\n      <Card.Footer className=\"d-flex justify-content-between align-items-center\">\r\n        <small className=\"text-muted\">\r\n          <Shield size={14} className=\"me-1\" />\r\n          Changes are saved to the backend\r\n        </small>\r\n        <Button\r\n          variant=\"primary\"\r\n          onClick={handleSave}\r\n          disabled={isSaving}\r\n          className=\"px-4\"\r\n        >\r\n          {isSaving ? (\r\n            <>\r\n              <Spinner as=\"span\" animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n              Saving...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Save size={18} className=\"me-2\" />\r\n              Save Settings\r\n            </>\r\n          )}\r\n        </Button>\r\n      </Card.Footer>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default GeneralSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SACEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAC9C,iBAAiB;AACxB,SACEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAER,KAAK,IAAIS,SAAS,EACnDC,MAAM,EAAEC,IAAI,EAAEC,MAAM,QACf,cAAc;AACrB,OAAOC,iBAAiB,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,OAAO;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAG1B,UAAU,CAAC,CAAC;EAElE,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM6C,QAAQ,GAAG,uBAAuB;EAE1C5C,SAAS,CAAC,MAAM;IACd,IAAIyB,OAAO,EAAE;MACXI,cAAc,CAACJ,OAAO,CAACG,WAAW,IAAI,EAAE,CAAC;MACzCG,uBAAuB,CAACN,OAAO,CAACK,oBAAoB,IAAI,EAAE,CAAC;MAC3DG,wBAAwB,CAACR,OAAO,CAACO,qBAAqB,IAAI,EAAE,CAAC;MAE7D,IAAIP,OAAO,CAACoB,QAAQ,EAAE;QACpB,MAAMC,cAAc,GAAGrB,OAAO,CAACoB,QAAQ,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QAC3D,MAAMC,OAAO,GAAG,GAAGJ,QAAQ,IAAIE,cAAc,EAAE;QAC/CX,cAAc,CAACa,OAAO,CAAC;MACzB,CAAC,MAAM;QACLb,cAAc,CAAC,cAAc,CAAC;MAChC;IACF;EACF,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC;EAGX,MAAMwB,gBAAgB,GAAIC,CAAC,IAAK;IAAA,IAAAC,eAAA;IAC9B,MAAMC,IAAI,IAAAD,eAAA,GAAGD,CAAC,CAACG,MAAM,CAACC,KAAK,cAAAH,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAI,CAACC,IAAI,EAAE;IAEX,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnCf,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,IAAIW,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BhB,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACF;IAEAJ,mBAAmB,CAACe,IAAI,CAAC;IACzBjB,cAAc,CAACuB,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IACzCX,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMmB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAChC,WAAW,CAACiC,IAAI,CAAC,CAAC,EAAE;MACvBpB,QAAQ,CAAC,uBAAuB,CAAC;MACjC;IACF;IAEAF,WAAW,CAAC,IAAI,CAAC;IACjBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMf,gBAAgB,CAAC;QACrBE,WAAW,EAAEA,WAAW,CAACiC,IAAI,CAAC,CAAC;QAC/B/B,oBAAoB,EAAEA,oBAAoB,CAAC+B,IAAI,CAAC,CAAC;QACjD7B,qBAAqB,EAAEA,qBAAqB,CAAC6B,IAAI,CAAC;MACpD,CAAC,EAAEzB,gBAAgB,CAAC;MAEpB,MAAMT,cAAc,CAAC,CAAC;MACtBmC,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZtB,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRF,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACEnB,OAAA,CAAClB,IAAI;IAAC8D,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAClC7C,OAAA,CAAClB,IAAI,CAACgE,MAAM;MAACF,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAChD7C,OAAA,CAACT,QAAQ;QAAC8C,IAAI,EAAE,EAAG;QAACO,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,qCAEzC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eACdlD,OAAA,CAAClB,IAAI,CAACqE,IAAI;MAAAN,QAAA,GACPzB,KAAK,iBAAIpB,OAAA,CAACb,KAAK;QAACiE,OAAO,EAAC,QAAQ;QAACR,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAEzB;MAAK;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAElElD,OAAA,CAACjB,IAAI;QAAA8D,QAAA,gBACH7C,OAAA,CAACZ,GAAG;UAAAyD,QAAA,gBACF7C,OAAA,CAACX,GAAG;YAACgE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACT7C,OAAA,CAACjB,IAAI,CAACuE,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B7C,OAAA,CAACjB,IAAI,CAACwE,KAAK;gBAACX,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBAC/C7C,OAAA,CAACR,KAAK;kBAAC6C,IAAI,EAAE,EAAG;kBAACO,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEnD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblD,OAAA,CAACjB,IAAI,CAACyE,OAAO;gBACXrB,IAAI,EAAC,MAAM;gBACXsB,KAAK,EAAEjD,WAAY;gBACnBkD,QAAQ,EAAG5B,CAAC,IAAKrB,cAAc,CAACqB,CAAC,CAACG,MAAM,CAACwB,KAAK,CAAE;gBAChDE,WAAW,EAAC,sBAAsB;gBAClCC,QAAQ;gBACRhB,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFlD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;gBAACjB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENlD,OAAA,CAACX,GAAG;YAACgE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACT7C,OAAA,CAACjB,IAAI,CAACuE,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B7C,OAAA,CAACjB,IAAI,CAACwE,KAAK;gBAACX,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBAC/C7C,OAAA,CAACP,QAAQ;kBAAC4C,IAAI,EAAE,EAAG;kBAACO,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAEtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblD,OAAA,CAACjB,IAAI,CAACyE,OAAO;gBACXrB,IAAI,EAAC,MAAM;gBACXsB,KAAK,EAAE/C,oBAAqB;gBAC5BgD,QAAQ,EAAG5B,CAAC,IAAKnB,uBAAuB,CAACmB,CAAC,CAACG,MAAM,CAACwB,KAAK,CAAE;gBACzDb,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFlD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;gBAACjB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACZ,GAAG;UAAAyD,QAAA,eACF7C,OAAA,CAACX,GAAG;YAACgE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACT7C,OAAA,CAACjB,IAAI,CAACuE,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B7C,OAAA,CAACjB,IAAI,CAACwE,KAAK;gBAACX,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBAC/C7C,OAAA,CAACP,QAAQ;kBAAC4C,IAAI,EAAE,EAAG;kBAACO,SAAS,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,yBAErD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblD,OAAA,CAACjB,IAAI,CAACyE,OAAO;gBACXrB,IAAI,EAAC,MAAM;gBACXsB,KAAK,EAAE7C,qBAAsB;gBAC7B8C,QAAQ,EAAG5B,CAAC,IAAKjB,wBAAwB,CAACiB,CAAC,CAACG,MAAM,CAACwB,KAAK,CAAE;gBAC1Db,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFlD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;gBAACjB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACjB,IAAI,CAACuE,KAAK;UAACV,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1B7C,OAAA,CAACjB,IAAI,CAACwE,KAAK;YAACX,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBAC/C7C,OAAA,CAACN,SAAS;cAAC2C,IAAI,EAAE,EAAG;cAACO,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEvD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblD,OAAA;YAAK4C,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnE7C,OAAA,CAACZ,GAAG;cAACwD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC7C,OAAA,CAACX,GAAG;gBAACgE,EAAE,EAAE,CAAE;gBAACT,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACjC7C,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAChE7C,OAAA,CAACf,KAAK;oBACP6E,GAAG,EAAEhD,WAAY;oBACjBiD,aAAa;oBACbC,KAAK,EAAC,IAAI;oBACVC,MAAM,EAAC,IAAI;oBACXrB,SAAS,EAAC,wCAAwC;oBAClDsB,OAAO,EAAGpC,CAAC,IAAK;sBACdA,CAAC,CAACG,MAAM,CAACkC,OAAO,GAAG,IAAI;sBACvBrC,CAAC,CAACG,MAAM,CAAC6B,GAAG,GAAG,cAAc,CAAC,CAAC;oBACjC;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGkBlD,OAAA;oBAAK4C,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,eAC7E7C,OAAA,CAACL,MAAM;sBAAC0C,IAAI,EAAE,EAAG;sBAACO,SAAS,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA,CAACX,GAAG;gBAACgE,EAAE,EAAE,CAAE;gBAAAR,QAAA,gBACT7C,OAAA,CAACjB,IAAI,CAACyE,OAAO;kBACXrB,IAAI,EAAC,MAAM;kBACXiC,MAAM,EAAC,SAAS;kBAChBV,QAAQ,EAAE7B,gBAAiB;kBAC3Be,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFlD,OAAA;kBAAK4C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzD7C,OAAA,CAACJ,IAAI;oBAACyC,IAAI,EAAE,EAAG;oBAACO,SAAS,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iEAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEZlD,OAAA,CAAClB,IAAI,CAACuF,MAAM;MAACzB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBACxE7C,OAAA;QAAO4C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAC3B7C,OAAA,CAACH,MAAM;UAACwC,IAAI,EAAE,EAAG;UAACO,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oCAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRlD,OAAA,CAAChB,MAAM;QACLoE,OAAO,EAAC,SAAS;QACjBkB,OAAO,EAAE9B,UAAW;QACpB+B,QAAQ,EAAErD,QAAS;QACnB0B,SAAS,EAAC,MAAM;QAAAC,QAAA,EAEf3B,QAAQ,gBACPlB,OAAA,CAAAE,SAAA;UAAA2C,QAAA,gBACE7C,OAAA,CAACd,OAAO;YAACsF,EAAE,EAAC,MAAM;YAACC,SAAS,EAAC,QAAQ;YAACpC,IAAI,EAAC,IAAI;YAACO,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAErE;QAAA,eAAE,CAAC,gBAEHlD,OAAA,CAAAE,SAAA;UAAA2C,QAAA,gBACE7C,OAAA,CAACV,IAAI;YAAC+C,IAAI,EAAE,EAAG;YAACO,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAErC;QAAA,eAAE;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAAC9C,EAAA,CAxNID,eAAe;EAAA,QACmCtB,UAAU;AAAA;AAAA6F,EAAA,GAD5DvE,eAAe;AA0NrB,eAAeA,eAAe;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}