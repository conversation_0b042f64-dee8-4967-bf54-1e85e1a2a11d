[{"C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\context\\AppDataContext.jsx": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\Layout.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\pages\\AdminPage.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\services\\api.js": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\SpeakerManager.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\ScheduleManager.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\GeneralSettings.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\common\\NotificationModal.jsx": "11"}, {"size": 600, "mtime": 1751112883606, "results": "12", "hashOfConfig": "13"}, {"size": 731, "mtime": 1751112883600, "results": "14", "hashOfConfig": "13"}, {"size": 375, "mtime": 1751112883608, "results": "15", "hashOfConfig": "13"}, {"size": 8762, "mtime": 1751154498346, "results": "16", "hashOfConfig": "13"}, {"size": 3545, "mtime": 1751151143703, "results": "17", "hashOfConfig": "13"}, {"size": 4086, "mtime": 1751155451177, "results": "18", "hashOfConfig": "13"}, {"size": 3806, "mtime": 1751147217544, "results": "19", "hashOfConfig": "13"}, {"size": 7042, "mtime": 1751155689130, "results": "20", "hashOfConfig": "13"}, {"size": 8973, "mtime": 1751149563748, "results": "21", "hashOfConfig": "13"}, {"size": 7893, "mtime": 1751155585638, "results": "22", "hashOfConfig": "13"}, {"size": 2128, "mtime": 1751155515241, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "i5ec9h", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\context\\AppDataContext.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\pages\\AdminPage.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\SpeakerManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\ScheduleManager.jsx", ["57", "58", "59", "60", "61", "62"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\admin\\GeneralSettings.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\SITE2025\\my-bootstrap-admin\\src\\components\\common\\NotificationModal.jsx", [], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 3, "column": 76, "nodeType": "65", "messageId": "66", "endLine": 3, "endColumn": 83}, {"ruleId": "63", "severity": 1, "message": "67", "line": 4, "column": 18, "nodeType": "65", "messageId": "66", "endLine": 4, "endColumn": 22}, {"ruleId": "63", "severity": 1, "message": "68", "line": 9, "column": 10, "nodeType": "65", "messageId": "66", "endLine": 9, "endColumn": 16}, {"ruleId": "63", "severity": 1, "message": "69", "line": 9, "column": 18, "nodeType": "65", "messageId": "66", "endLine": 9, "endColumn": 27}, {"ruleId": "63", "severity": 1, "message": "70", "line": 12, "column": 10, "nodeType": "65", "messageId": "66", "endLine": 12, "endColumn": 18}, {"ruleId": "63", "severity": 1, "message": "71", "line": 12, "column": 20, "nodeType": "65", "messageId": "66", "endLine": 12, "endColumn": 31}, "no-unused-vars", "'Spinner' is defined but never used.", "Identifier", "unusedVar", "'Save' is defined but never used.", "'newDay' is assigned a value but never used.", "'setNewDay' is assigned a value but never used.", "'isSaving' is assigned a value but never used.", "'setIsSaving' is assigned a value but never used."]