{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\GitHub\\\\SITE2025\\\\my-bootstrap-admin\\\\src\\\\components\\\\common\\\\NotificationModal.jsx\";\nimport React from 'react';\nimport { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';\nimport { CheckCircle, AlertTriangle, Info, XCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotificationModal = ({\n  show,\n  onHide,\n  title,\n  message,\n  type = 'info',\n  // 'success', 'warning', 'error', 'info'\n  onConfirm = null,\n  // If provided, shows confirm/cancel buttons\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  confirmVariant = 'primary'\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 24,\n          className: \"text-success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n          size: 24,\n          className: \"text-warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          size: 24,\n          className: \"text-danger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Info, {\n          size: 24,\n          className: \"text-info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getHeaderClass = () => {\n    switch (type) {\n      case 'success':\n        return 'border-success';\n      case 'warning':\n        return 'border-warning';\n      case 'error':\n        return 'border-danger';\n      default:\n        return 'border-info';\n    }\n  };\n  const handleConfirm = () => {\n    if (onConfirm) {\n      onConfirm();\n    }\n    onHide();\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      className: `border-bottom-0 ${getHeaderClass()}`,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      className: \"py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-0\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      className: \"border-top-0\",\n      children: onConfirm ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: onHide,\n          children: cancelText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: confirmVariant,\n          onClick: handleConfirm,\n          children: confirmText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: onHide,\n        children: \"OK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = NotificationModal;\nexport default NotificationModal;\nvar _c;\n$RefreshReg$(_c, \"NotificationModal\");", "map": {"version": 3, "names": ["React", "Modal", "<PERSON><PERSON>", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "XCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotificationModal", "show", "onHide", "title", "message", "type", "onConfirm", "confirmText", "cancelText", "confirmVariant", "getIcon", "size", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getHeaderClass", "handleConfirm", "centered", "children", "Header", "closeButton", "Title", "Body", "Footer", "variant", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/SITE2025/my-bootstrap-admin/src/components/common/NotificationModal.jsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport { CheckCircle, AlertTriangle, Info, XCircle } from 'lucide-react';\n\nconst NotificationModal = ({ \n  show, \n  onHide, \n  title, \n  message, \n  type = 'info', // 'success', 'warning', 'error', 'info'\n  onConfirm = null, // If provided, shows confirm/cancel buttons\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  confirmVariant = 'primary'\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle size={24} className=\"text-success\" />;\n      case 'warning':\n        return <AlertTriangle size={24} className=\"text-warning\" />;\n      case 'error':\n        return <XCircle size={24} className=\"text-danger\" />;\n      default:\n        return <Info size={24} className=\"text-info\" />;\n    }\n  };\n\n  const getHeaderClass = () => {\n    switch (type) {\n      case 'success':\n        return 'border-success';\n      case 'warning':\n        return 'border-warning';\n      case 'error':\n        return 'border-danger';\n      default:\n        return 'border-info';\n    }\n  };\n\n  const handleConfirm = () => {\n    if (onConfirm) {\n      onConfirm();\n    }\n    onHide();\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} centered>\n      <Modal.Header closeButton className={`border-bottom-0 ${getHeaderClass()}`}>\n        <Modal.Title className=\"d-flex align-items-center\">\n          {getIcon()}\n          <span className=\"ms-2\">{title}</span>\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body className=\"py-4\">\n        <p className=\"mb-0\">{message}</p>\n      </Modal.Body>\n      <Modal.Footer className=\"border-top-0\">\n        {onConfirm ? (\n          <>\n            <Button variant=\"secondary\" onClick={onHide}>\n              {cancelText}\n            </Button>\n            <Button variant={confirmVariant} onClick={handleConfirm}>\n              {confirmText}\n            </Button>\n          </>\n        ) : (\n          <Button variant=\"primary\" onClick={onHide}>\n            OK\n          </Button>\n        )}\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default NotificationModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,MAAM,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,aAAa,EAAEC,IAAI,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,IAAI;EACJC,MAAM;EACNC,KAAK;EACLC,OAAO;EACPC,IAAI,GAAG,MAAM;EAAE;EACfC,SAAS,GAAG,IAAI;EAAE;EAClBC,WAAW,GAAG,SAAS;EACvBC,UAAU,GAAG,QAAQ;EACrBC,cAAc,GAAG;AACnB,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQL,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOR,OAAA,CAACL,WAAW;UAACmB,IAAI,EAAE,EAAG;UAACC,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,SAAS;QACZ,oBAAOnB,OAAA,CAACJ,aAAa;UAACkB,IAAI,EAAE,EAAG;UAACC,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,OAAO;QACV,oBAAOnB,OAAA,CAACF,OAAO;UAACgB,IAAI,EAAE,EAAG;UAACC,SAAS,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAOnB,OAAA,CAACH,IAAI;UAACiB,IAAI,EAAE,EAAG;UAACC,SAAS,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACnD;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQZ,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,gBAAgB;MACzB,KAAK,SAAS;QACZ,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,eAAe;MACxB;QACE,OAAO,aAAa;IACxB;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,SAAS,EAAE;MACbA,SAAS,CAAC,CAAC;IACb;IACAJ,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEL,OAAA,CAACP,KAAK;IAACW,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAACiB,QAAQ;IAAAC,QAAA,gBACzCvB,OAAA,CAACP,KAAK,CAAC+B,MAAM;MAACC,WAAW;MAACV,SAAS,EAAE,mBAAmBK,cAAc,CAAC,CAAC,EAAG;MAAAG,QAAA,eACzEvB,OAAA,CAACP,KAAK,CAACiC,KAAK;QAACX,SAAS,EAAC,2BAA2B;QAAAQ,QAAA,GAC/CV,OAAO,CAAC,CAAC,eACVb,OAAA;UAAMe,SAAS,EAAC,MAAM;UAAAQ,QAAA,EAAEjB;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACfnB,OAAA,CAACP,KAAK,CAACkC,IAAI;MAACZ,SAAS,EAAC,MAAM;MAAAQ,QAAA,eAC1BvB,OAAA;QAAGe,SAAS,EAAC,MAAM;QAAAQ,QAAA,EAAEhB;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eACbnB,OAAA,CAACP,KAAK,CAACmC,MAAM;MAACb,SAAS,EAAC,cAAc;MAAAQ,QAAA,EACnCd,SAAS,gBACRT,OAAA,CAAAE,SAAA;QAAAqB,QAAA,gBACEvB,OAAA,CAACN,MAAM;UAACmC,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEzB,MAAO;UAAAkB,QAAA,EACzCZ;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACTnB,OAAA,CAACN,MAAM;UAACmC,OAAO,EAAEjB,cAAe;UAACkB,OAAO,EAAET,aAAc;UAAAE,QAAA,EACrDb;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACT,CAAC,gBAEHnB,OAAA,CAACN,MAAM;QAACmC,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEzB,MAAO;QAAAkB,QAAA,EAAC;MAE3C;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAACY,EAAA,GAzEI5B,iBAAiB;AA2EvB,eAAeA,iBAAiB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}